"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useEffect, useState } from "react"
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Lightbulb,
  Edit,
  Monitor,
  Send,
  CreditCard,
  Users,
  ArrowRight,
  Star,
  MapPin,
  Phone,
  Mail,
  Play,
  Award,
  TrendingUp,
  Zap,
  Eye,
  MousePointer,
} from "lucide-react"

export default function MappleAgencyWebsite() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isLoaded, setIsLoaded] = useState(false)
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  useEffect(() => {
    setIsLoaded(true)
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }
    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  const testimonials = [
    {
      quote: "Trabajos increíbles y sobre todo mucha responsabilidad",
      text: "El equipo de Mapple Agency trabaja con mucha profesionalidad y entregan a tiempo, lo que más me gustó es que te responden al instante y te dan un buen soporte.",
      author: "Yadira Lantigua",
      position: "CEO, CONSTRUCTORA RODRIGUEZ",
      avatar: "YL",
    },
    {
      quote: "Superaron todas nuestras expectativas",
      text: "La creatividad y profesionalismo del equipo de Mapple Agency transformó completamente nuestra presencia digital. Los resultados hablan por sí solos.",
      author: "Carlos Mendoza",
      position: "Director, CENTRO MÉDICO BOURRIGAL",
      avatar: "CM",
    },
    {
      quote: "ROI excepcional en marketing digital",
      text: "Gracias a sus estrategias de marketing digital, hemos triplicado nuestras ventas online. Su enfoque data-driven es impresionante.",
      author: "Ana Rodríguez",
      position: "CMO, TECH SOLUTIONS",
      avatar: "AR",
    },
  ]

  const stats = [
    { number: "150+", label: "Proyectos Completados", icon: Award },
    { number: "98%", label: "Clientes Satisfechos", icon: Star },
    { number: "300%", label: "ROI Promedio", icon: TrendingUp },
    { number: "24/7", label: "Soporte Técnico", icon: Zap },
  ]

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden">
      {/* Custom cursor */}
      <div
        className="fixed w-4 h-4 bg-gradient-to-r from-cyan-400 to-magenta-500 rounded-full pointer-events-none z-50 mix-blend-difference transition-transform duration-100 ease-out"
        style={{
          left: mousePosition.x - 8,
          top: mousePosition.y - 8,
          transform: `scale(${isLoaded ? 1 : 0})`,
        }}
      />

      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-40 bg-black/90 backdrop-blur-md border-b border-gray-800/50 transition-all duration-300">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3 group">
            <Image
              src="/mapple-logo.png"
              alt="Mapple Agency"
              width={180}
              height={60}
              className="h-12 w-auto transition-transform duration-300 group-hover:scale-105"
            />
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            {["Home", "Sobre Nosotros", "Servicios", "Portafolio"].map((item, index) => (
              <Link
                key={index}
                href={`#${item.toLowerCase().replace(" ", "-")}`}
                className="text-white hover:text-cyan-400 transition-all duration-300 relative group py-2"
              >
                {item}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-magenta-500 group-hover:w-full transition-all duration-300"></span>
              </Link>
            ))}
          </nav>

          <Button className="bg-gradient-to-r from-magenta-500 to-pink-600 hover:from-magenta-600 hover:to-pink-700 text-white px-6 py-2 rounded-lg font-semibold shadow-lg shadow-magenta-500/25 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-magenta-500/40 group">
            <span className="group-hover:scale-110 transition-transform duration-200">CONTÁCTANOS</span>
          </Button>
        </div>
      </header>

      {/* Animated gradient line */}
      <div className="h-1 bg-gradient-to-r from-cyan-400 via-magenta-500 to-pink-600 mt-[73px] animate-pulse"></div>

      {/* Hero Section */}
      <section className="relative py-32 overflow-hidden">
        {/* Animated background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-cyan-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-magenta-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-pink-500/5 rounded-full blur-2xl animate-ping"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div
              className={`space-y-8 transition-all duration-1000 ${isLoaded ? "translate-x-0 opacity-100" : "-translate-x-10 opacity-0"}`}
            >
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                  <span className="text-cyan-400 font-medium">Agencia Digital Premium</span>
                </div>
                <h1 className="text-6xl lg:text-7xl font-bold leading-tight">
                  <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent animate-fade-in">
                    Creamos
                  </span>
                  <br />
                  <span className="bg-gradient-to-r from-cyan-400 to-magenta-500 bg-clip-text text-transparent animate-fade-in delay-200">
                    Experiencias
                  </span>
                  <br />
                  <span className="bg-gradient-to-r from-magenta-500 to-pink-600 bg-clip-text text-transparent animate-fade-in delay-400">
                    Impactantes
                  </span>
                </h1>
              </div>
              <p className="text-gray-300 text-xl max-w-lg leading-relaxed animate-fade-in delay-600">
                ¿Listo para comenzar? Cuéntanos sobre tu proyecto y tus objetivos. Estamos aquí para discutir cómo
                podemos ayudarte a lograr el éxito en línea.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 animate-fade-in delay-800">
                <Button className="bg-gradient-to-r from-magenta-500 to-pink-600 hover:from-magenta-600 hover:to-pink-700 text-white px-8 py-4 rounded-lg font-semibold text-lg shadow-lg shadow-magenta-500/25 transition-all duration-300 hover:scale-105 group relative overflow-hidden">
                  <span className="relative z-10 flex items-center">
                    INICIA YA!
                    <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-magenta-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Button>
                <Button
                  variant="outline"
                  className="border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 bg-transparent group relative overflow-hidden"
                >
                  <span className="relative z-10 flex items-center">
                    <Play className="mr-2 w-5 h-5" />
                    Ver Demo
                  </span>
                </Button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 pt-8 animate-fade-in delay-1000">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center group">
                    <div className="flex items-center justify-center mb-2">
                      <stat.icon className="w-6 h-6 text-cyan-400 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <div className="text-2xl font-bold text-white group-hover:text-cyan-400 transition-colors">
                      {stat.number}
                    </div>
                    <div className="text-sm text-gray-400">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Enhanced 3D geometric M shape */}
            <div
              className={`relative flex justify-center items-center transition-all duration-1000 delay-500 ${isLoaded ? "translate-x-0 opacity-100" : "translate-x-10 opacity-0"}`}
            >
              <div className="relative w-96 h-96 group">
                <div className="absolute inset-0 transition-transform duration-500 group-hover:scale-105">
                  <div className="w-24 h-80 bg-gradient-to-b from-gray-700 to-gray-800 transform rotate-12 absolute left-12 top-8 rounded-lg shadow-2xl transition-all duration-500 group-hover:shadow-cyan-400/20 group-hover:rotate-[15deg]"></div>
                  <div className="w-24 h-80 bg-gradient-to-b from-gray-600 to-gray-700 transform -rotate-12 absolute right-12 top-8 rounded-lg shadow-2xl transition-all duration-500 group-hover:shadow-magenta-500/20 group-hover:-rotate-[15deg]"></div>
                  <div className="w-20 h-20 bg-gradient-to-br from-cyan-400 to-magenta-500 transform rotate-45 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-lg shadow-lg shadow-cyan-400/25 transition-all duration-500 group-hover:rotate-[60deg] group-hover:scale-110"></div>
                </div>
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-cyan-400 rounded-full animate-bounce"></div>
                <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-magenta-500 rounded-full animate-bounce delay-1000"></div>
                <div className="absolute top-1/4 -left-8 w-4 h-4 bg-pink-400 rounded-full animate-pulse delay-500"></div>
                <div className="absolute bottom-1/4 -right-8 w-5 h-5 bg-yellow-400 rounded-full animate-pulse delay-1500"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-24 bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900 relative">
        <div
          className="absolute inset-0 bg-gray-50 opacity-30"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fillRule='evenodd'%3E%3Cg fill='%23f0f0f0' fillOpacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: "60px 60px",
          }}
        ></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full mb-6">
              <Eye className="w-4 h-4 text-magenta-500" />
              <span className="text-sm font-medium text-gray-600">Nuestros Servicios</span>
            </div>
            <h2 className="text-5xl font-bold text-gray-800 mb-6">
              <span className="bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                Servicios{" "}
              </span>
              <span className="bg-gradient-to-r from-cyan-500 to-magenta-600 bg-clip-text text-transparent">
                Digitales
              </span>
            </h2>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg leading-relaxed">
              Deja todo en nuestras manos, aquí encontrarás lo que necesitas para que puedas posicionar tu negocio de
              manera digital.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Edit,
                title: "Diseño Gráfico",
                description:
                  "Nuestro equipo de diseñadores gráficos talentosos dan vida a tus ideas con creatividad y originalidad, asegurando que tu marca se destaque.",
                color: "from-blue-500 to-cyan-500",
                features: ["Branding", "UI/UX", "Print Design"],
              },
              {
                icon: Monitor,
                title: "Desarrollo Web",
                description:
                  "Diseñamos y desarrollamos sitios web personalizados que no solo se ven increíbles, sino que también funcionan de manera efectiva para alcanzar tus objetivos.",
                color: "from-green-500 to-emerald-500",
                features: ["React/Next.js", "E-commerce", "CMS"],
              },
              {
                icon: Send,
                title: "Marketing Digital",
                description:
                  "Nuestra experiencia en marketing digital garantiza que tu negocio sea encontrado por el público adecuado en el momento adecuado, maximizando tu alcance y conversión.",
                color: "from-purple-500 to-pink-500",
                features: ["SEO/SEM", "Social Media", "Analytics"],
              },
              {
                icon: Lightbulb,
                title: "Branding Corporativo",
                description:
                  "Desde logotipos icónicos hasta narrativas de marca convincentes, creamos una identidad visual y verbal que deja una impresión duradera en la mente de tus clientes.",
                color: "from-yellow-500 to-orange-500",
                features: ["Logo Design", "Brand Guide", "Naming"],
              },
              {
                icon: CreditCard,
                title: "Ecommerce",
                description:
                  "Creamos experiencias de compra en línea que no solo son funcionales, sino que también encantan a tus clientes. Deja que tu ecommerce hable por sí mismo.",
                color: "from-red-500 to-pink-500",
                features: ["Shopify", "WooCommerce", "Payments"],
              },
              {
                icon: Users,
                title: "Soporte Técnico 24hrs",
                description:
                  "Más allá de los servicios que ofrecemos, estamos aquí para apoyarte las 24 horas. Tu éxito es nuestra prioridad y eso significa estar a tu lado siempre.",
                color: "from-indigo-500 to-purple-500",
                features: ["24/7 Support", "Maintenance", "Updates"],
              },
            ].map((service, index) => (
              <Card
                key={index}
                className="bg-white/80 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-gray-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <CardContent className="p-8 text-center relative z-10">
                  <div
                    className={`w-16 h-16 bg-gradient-to-br ${service.color} rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 relative`}
                  >
                    <service.icon className="w-8 h-8 text-white" />
                    <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-gray-900 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors mb-4">
                    {service.description}
                  </p>
                  <div className="flex flex-wrap justify-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-2 group-hover:translate-y-0">
                    {service.features.map((feature, idx) => (
                      <span key={idx} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        {feature}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-24 bg-black relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-400/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-magenta-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full mb-6">
              <MousePointer className="w-4 h-4 text-cyan-400" />
              <span className="text-sm font-medium text-gray-300">Portfolio</span>
            </div>
            <h2 className="text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-magenta-500 to-pink-600 bg-clip-text text-transparent">
                Nuestro trabajo habla
              </span>
              <br />
              <span className="bg-gradient-to-r from-cyan-400 to-magenta-500 bg-clip-text text-transparent">
                por sí mismo!
              </span>
            </h2>
            <p className="text-gray-300 text-lg">Explora una selección de nuestros proyectos más destacados.</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[
              {
                bg: "bg-gradient-to-br from-green-100 to-green-200",
                title: "Skincare Products",
                category: "E-commerce",
              },
              {
                bg: "bg-gradient-to-br from-gray-700 to-gray-900",
                title: "Supplement Packaging",
                category: "Branding",
              },
              { bg: "bg-gradient-to-br from-blue-100 to-blue-200", title: "Website Design", category: "Web Design" },
              { bg: "bg-gradient-to-br from-amber-100 to-amber-200", title: "Coffee Packaging", category: "Packaging" },
            ].map((item, index) => (
              <div
                key={index}
                className={`${item.bg} rounded-2xl overflow-hidden group cursor-pointer transition-all duration-500 hover:scale-105 hover:shadow-2xl relative`}
              >
                <div className="h-64 flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                  <div className="absolute inset-0 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0">
                    <span className="text-white font-semibold text-lg mb-2">Ver Proyecto</span>
                    <span className="text-cyan-400 text-sm">{item.category}</span>
                  </div>
                  <Image
                    src={`/placeholder.svg?height=300&width=300&text=${item.title}`}
                    alt={item.title}
                    width={300}
                    height={300}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Button className="bg-gradient-to-r from-magenta-500 to-pink-600 hover:from-magenta-600 hover:to-pink-700 text-white px-10 py-4 rounded-lg font-semibold text-lg shadow-lg shadow-magenta-500/25 transition-all duration-300 hover:scale-105 group relative overflow-hidden">
              <span className="relative z-10 flex items-center">
                VER MÁS
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-magenta-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full mb-6">
              <Star className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium text-gray-600">Testimonios</span>
            </div>
            <h2 className="text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-magenta-500 to-pink-600 bg-clip-text text-transparent">
                Estás en buenas manos.
              </span>
            </h2>
            <p className="text-gray-600 text-lg">Empresas que han confiado en nosotros.</p>
          </div>

          {/* Client Logos */}
          <div className="flex justify-center items-center space-x-16 mb-20">
            {["Centro Médico Bourrigal", "Colegio Logo", "Constructora Rodriguez"].map((client, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group"
              >
                <Image
                  src={`/placeholder.svg?height=80&width=120&text=${client}`}
                  alt={client}
                  width={120}
                  height={80}
                  className="grayscale group-hover:grayscale-0 transition-all duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Dynamic Testimonial Quote Section */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-cyan-400/10 to-magenta-500/10 rounded-full blur-3xl animate-pulse"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-center mb-8">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className="w-8 h-8 text-yellow-400 fill-current animate-pulse"
                  style={{ animationDelay: `${i * 200}ms` }}
                />
              ))}
            </div>

            {/* Dynamic testimonial content */}
            <div className="transition-all duration-500 transform">
              <blockquote className="text-3xl lg:text-4xl font-bold mb-8">
                <span className="bg-gradient-to-r from-magenta-500 to-pink-600 bg-clip-text text-transparent">
                  "{testimonials[currentTestimonial].quote}"
                </span>
              </blockquote>
              <p className="text-gray-300 text-lg mb-12 max-w-3xl mx-auto leading-relaxed">
                {testimonials[currentTestimonial].text}
              </p>
              <div className="flex items-center justify-center space-x-6">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-magenta-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {testimonials[currentTestimonial].avatar}
                </div>
                <div className="text-left">
                  <p className="font-bold text-white text-lg">{testimonials[currentTestimonial].author}</p>
                  <p className="text-cyan-400 font-medium">{testimonials[currentTestimonial].position}</p>
                </div>
              </div>
            </div>

            {/* Testimonial indicators */}
            <div className="flex justify-center space-x-2 mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentTestimonial
                      ? "bg-gradient-to-r from-cyan-400 to-magenta-500 scale-125"
                      : "bg-gray-600 hover:bg-gray-500"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Location Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full mb-6">
              <MapPin className="w-4 h-4 text-magenta-500" />
              <span className="text-sm font-medium text-gray-600">Ubicación</span>
            </div>
            <h2 className="text-5xl font-bold text-gray-800 mb-8">
              <span className="bg-gradient-to-r from-gray-800 to-cyan-600 bg-clip-text text-transparent">
                Visítanos
              </span>
            </h2>
          </div>

          <div className="bg-white rounded-3xl overflow-hidden shadow-2xl mb-16 group">
            <div className="h-96 bg-gradient-to-br from-green-200 via-blue-200 to-cyan-200 relative transition-all duration-500 group-hover:scale-105">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-white/90 backdrop-blur-sm p-6 rounded-2xl shadow-xl transition-all duration-300 group-hover:scale-110">
                  <MapPin className="w-8 h-8 text-magenta-500 mx-auto mb-2 animate-bounce" />
                  <p className="text-gray-600 text-sm mb-1">Interactive Map</p>
                  <p className="font-bold text-gray-800">Puerto Plata, República Dominicana</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-3xl p-10 shadow-2xl">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-bold text-gray-800 mb-6">¿Necesitas ayuda o tienes preguntas?</h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  Nuestro equipo está disponible las 24 horas para resolver tus consultas y brindarte la asistencia que
                  necesitas.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 text-gray-600 group">
                    <div className="w-10 h-10 bg-gradient-to-br from-magenta-500 to-pink-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Phone className="w-5 h-5 text-white" />
                    </div>
                    <span className="group-hover:text-magenta-500 transition-colors">24/7 Support</span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-600 group">
                    <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Mail className="w-5 h-5 text-white" />
                    </div>
                    <span className="group-hover:text-cyan-500 transition-colors">Respuesta inmediata</span>
                  </div>
                </div>
              </div>
              <div className="text-center md:text-right">
                <Button className="bg-gradient-to-r from-magenta-500 to-pink-600 hover:from-magenta-600 hover:to-pink-700 text-white px-10 py-4 rounded-lg font-semibold text-lg shadow-lg shadow-magenta-500/25 transition-all duration-300 hover:scale-105 group relative overflow-hidden">
                  <span className="relative z-10 flex items-center">
                    CONTÁCTANOS
                    <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-magenta-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-900 to-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-10 right-10 w-32 h-32 bg-cyan-400/5 rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 left-10 w-40 h-40 bg-magenta-500/5 rounded-full blur-2xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid md:grid-cols-3 gap-12 mb-12">
            <div>
              <div className="flex items-center space-x-3 mb-6 group">
                <Image
                  src="/mapple-logo.png"
                  alt="Mapple Agency"
                  width={180}
                  height={60}
                  className="h-12 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <p className="text-gray-300 mb-8 text-lg">Donde la Creatividad Encuentra su Camino Digital.</p>
              <div className="flex space-x-4">
                {[Facebook, Twitter, Instagram, Linkedin].map((Icon, index) => (
                  <div
                    key={index}
                    className="w-12 h-12 bg-gray-800 hover:bg-gradient-to-br hover:from-cyan-400 hover:to-magenta-500 rounded-full flex items-center justify-center cursor-pointer transition-all duration-300 hover:scale-110 group"
                  >
                    <Icon className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-magenta-500 bg-clip-text text-transparent">
                Menú
              </h4>
              <ul className="space-y-3">
                {["Sobre Nosotros", "Servicios", "Portfolio", "Blog", "Contacto"].map((item, index) => (
                  <li key={index}>
                    <Link
                      href="#"
                      className="text-gray-300 hover:text-cyan-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform -translate-x-2 group-hover:translate-x-0" />
                      {item}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="text-xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-magenta-500 bg-clip-text text-transparent">
                Contactos
              </h4>
              <div className="text-gray-300 space-y-4">
                <div className="flex items-start space-x-3 group">
                  <MapPin className="w-5 h-5 text-magenta-500 mt-1 flex-shrink-0 group-hover:scale-110 transition-transform duration-300" />
                  <div>
                    <p className="group-hover:text-white transition-colors">Av.27 de febrero #54, Puerto Plata,</p>
                    <p className="group-hover:text-white transition-colors">República Dominicana.</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 group">
                  <Mail className="w-5 h-5 text-magenta-500 group-hover:scale-110 transition-transform duration-300" />
                  <p className="group-hover:text-white transition-colors"><EMAIL></p>
                </div>
                <div className="flex items-center space-x-3 group">
                  <Phone className="w-5 h-5 text-magenta-500 group-hover:scale-110 transition-transform duration-300" />
                  <p className="group-hover:text-white transition-colors">************, ************</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="bg-gradient-to-r from-magenta-500 to-pink-600 text-center py-6 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-magenta-600 opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
            <p className="text-white font-medium relative z-10">
              Copyright © 2025 Mapple Agency Powered by Mapple Studios SRL
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
